use actix_web::{web, HttpRequest, HttpResponse, Responder};
use log::{info, error};
use serde_json::Value;

use crate::dto::{GetAiUsageResponse, ApiResponse, ErrorCode};
use crate::middleware::token_parser::get_user_id_from_request;
use crate::AppState;

/**
 * 获取用户AI使用次数处理函数
 *
 * 此函数处理GET /ai-usage/get请求，用于获取用户的AI使用次数
 *
 * 返回数据：
 * {
 *   "code": 0,
 *   "message": "获取成功",
 *   "data": {
 *     "id": "记录ID",
 *     "user_id": "用户ID",
 *     "usage_count": 剩余次数
 *   }
 * }
 *
 * @param req HTTP请求
 * @param app_state 应用状态
 * @return HTTP响应
 */
pub async fn handle(
    req: HttpRequest,
    app_state: web::Data<AppState>,
) -> impl Responder {
    // 获取当前用户ID
    let user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("未登录或身份验证失败");
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: ErrorCode::TokenInvalid.code(),
                message: "未登录或身份验证失败".to_string(),
                data: Value::Null,
            });
        }
    };

    // 解析用户ID
    let user_id = match user_id_str.parse::<u64>() {
        Ok(id) => id,
        Err(_) => {
            error!("用户ID格式无效: {}", user_id_str);
            return HttpResponse::BadRequest().json(ApiResponse {
                code: ErrorCode::InvalidParameter.code(),
                message: "用户ID格式无效".to_string(),
                data: Value::Null,
            });
        }
    };

    info!("获取用户AI使用次数，用户ID: {}", user_id);

    // 获取MySQL AI使用次数服务
    let mysql_ai_usage_service = match &app_state.mysql_ai_usage_service {
        Some(service) => service,
        None => {
            error!("MySQL AI使用次数服务不可用");
            return HttpResponse::ServiceUnavailable().json(ApiResponse {
                code: ErrorCode::ServiceUnavailable.code(),
                message: "服务暂时不可用".to_string(),
                data: Value::Null,
            });
        }
    };

    // 获取AI使用记录
    match mysql_ai_usage_service.get_usage_by_user_id(user_id).await {
        Ok(record) => {
            let record_id_str = record.id.to_string();
            let usage_count = record.usage_count.unwrap_or(0);
            info!("获取AI使用次数成功，用户ID: {}, 剩余次数: {}", user_id, usage_count);

            HttpResponse::Ok().json(ApiResponse {
                code: ErrorCode::Success.code(),
                message: "获取成功".to_string(),
                data: GetAiUsageResponse {
                    id: record_id_str,
                    user_id: user_id_str,
                    usage_count,
                },
            })
        }
        Err(e) => {
            error!("获取AI使用次数失败: {:?}", e);
            let (code, message) = match e {
                crate::services::mysql::MySqlAiUsageServiceError::RecordNotFound => {
                    (ErrorCode::NotFound.code(), "无权使用AI，联系管理员".to_string())
                }
                _ => (ErrorCode::DatabaseError.code(), "数据库错误".to_string()),
            };

            HttpResponse::InternalServerError().json(ApiResponse {
                code,
                message,
                data: Value::Null,
            })
        }
    }
}
